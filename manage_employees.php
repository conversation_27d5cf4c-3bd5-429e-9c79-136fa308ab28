<?php
session_start();

// Include permissions system
require_once __DIR__ . '/includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require employee management permissions
requireAnyPermission($mysqli, ['employees.create', 'employees.view', 'employees.edit', 'employees.delete']);

$msg = "";
$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['add_employee'])) {
    // Check permission for creating employees
    if (!hasPermission($mysqli, $_SESSION['user_id'], 'employees.create')) {
      $error = "You don't have permission to create employees.";
    } else {
      $name = trim($_POST['name'] ?? '');
      $email = trim($_POST['email'] ?? '');
      $phone = trim($_POST['phone'] ?? '');
      $department = trim($_POST['department'] ?? '');
      $position = trim($_POST['position'] ?? '');
      $hire_date = $_POST['hire_date'] ?? '';
      $create_account = isset($_POST['create_account']);
      $assigned_roles = $_POST['roles'] ?? [];

      if ($name) {
        // Start transaction
        $mysqli->begin_transaction();

        try {
          // Insert employee
          $stmt = $mysqli->prepare("INSERT INTO employees (name, email, phone, department, position, hire_date, status) VALUES (?, ?, ?, ?, ?, ?, 'active')");
          $stmt->bind_param('ssssss', $name, $email, $phone, $department, $position, $hire_date);

          if ($stmt->execute()) {
            $employee_id = $mysqli->insert_id;
            $stmt->close();

            // Create user account if requested
            if ($create_account) {
              $username = generateUsername($name);
              $password = generateSecurePassword();
              $password_hash = password_hash($password, PASSWORD_DEFAULT);

              // Check if username already exists
              $check_stmt = $mysqli->prepare("SELECT id FROM users WHERE username = ?");
              $check_stmt->bind_param('s', $username);
              $check_stmt->execute();
              $check_stmt->store_result();

              if ($check_stmt->num_rows > 0) {
                // Username exists, append employee ID
                $username = $username . '.' . $employee_id;
              }
              $check_stmt->close();

              // Insert user account
              $user_stmt = $mysqli->prepare("INSERT INTO users (employee_id, username, password_hash) VALUES (?, ?, ?)");
              $user_stmt->bind_param('iss', $employee_id, $username, $password_hash);

              if ($user_stmt->execute()) {
                $user_id = $mysqli->insert_id;
                $user_stmt->close();

                // Assign roles to user
                if (!empty($assigned_roles)) {
                  foreach ($assigned_roles as $role_id) {
                    assignRoleToUser($mysqli, $user_id, intval($role_id), $_SESSION['user_id']);
                  }
                }

                $mysqli->commit();
                $msg = "Employee added successfully! Username: $username, Password: $password (Please save this password securely)";
              } else {
                throw new Exception("Failed to create user account.");
              }
            } else {
              $mysqli->commit();
              $msg = "Employee added successfully!";
            }
          } else {
            throw new Exception("Failed to add employee.");
          }
        } catch (Exception $e) {
          $mysqli->rollback();
          $error = $e->getMessage();
        }
      } else {
        $error = "Name cannot be empty.";
      }
    }
  } elseif (isset($_POST['delete_employee']) && isset($_POST['employee_id'])) {
    // Check permission for deleting employees
    if (!hasPermission($mysqli, $_SESSION['user_id'], 'employees.delete')) {
      $error = "You don't have permission to delete employees.";
    } else {
      $employee_id = intval($_POST['employee_id']);

      // Start transaction
      $mysqli->begin_transaction();

      try {
        // Delete associated user account first (if exists)
        $user_stmt = $mysqli->prepare("DELETE FROM users WHERE employee_id = ?");
        $user_stmt->bind_param('i', $employee_id);
        $user_stmt->execute();
        $user_stmt->close();

        // Delete employee
        $emp_stmt = $mysqli->prepare("DELETE FROM employees WHERE id = ?");
        $emp_stmt->bind_param('i', $employee_id);

        if ($emp_stmt->execute()) {
          $emp_stmt->close();
          $mysqli->commit();
          $msg = "Employee and associated user account deleted successfully.";
        } else {
          throw new Exception("Failed to delete employee.");
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = $e->getMessage();
      }
    }
  }
}

// Get employees with their user account information
$result = $mysqli->query("
  SELECT e.id, e.name, e.email, e.phone, e.department, e.position, e.hire_date, e.status,
         u.id as user_id, u.username
  FROM employees e
  LEFT JOIN users u ON e.id = u.employee_id
  ORDER BY e.name
");
$employees = [];
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}

// Get all available roles for the form
$available_roles = getAllRoles($mysqli);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Employees - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <?php include 'includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header hover-lift">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Manage Employees</h1>
          <p class="page-subtitle">Add, edit, and manage your organization's employees</p>
        </div>
        <div class="d-flex align-items-center gap-3">
          <div class="badge bg-primary">
            <?= count($employees) ?> Total Employees
          </div>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Add Employee Form -->
    <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.create')): ?>
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-plus me-2"></i>
        Add New Employee
      </h2>
      <form method="POST">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="name" class="form-label">
                <i class="bi bi-person me-2"></i>
                Full Name *
              </label>
              <input
                type="text"
                class="form-control"
                name="name"
                id="name"
                required
                placeholder="Enter employee full name"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="email" class="form-label">
                <i class="bi bi-envelope me-2"></i>
                Email Address
              </label>
              <input
                type="email"
                class="form-control"
                name="email"
                id="email"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="phone" class="form-label">
                <i class="bi bi-telephone me-2"></i>
                Phone Number
              </label>
              <input
                type="tel"
                class="form-control"
                name="phone"
                id="phone"
                placeholder="+****************"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="hire_date" class="form-label">
                <i class="bi bi-calendar me-2"></i>
                Hire Date
              </label>
              <input
                type="date"
                class="form-control"
                name="hire_date"
                id="hire_date"
                value="<?= date('Y-m-d') ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="department" class="form-label">
                <i class="bi bi-building me-2"></i>
                Department
              </label>
              <input
                type="text"
                class="form-control"
                name="department"
                id="department"
                placeholder="e.g., Operations, HR, IT"
              />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="position" class="form-label">
                <i class="bi bi-briefcase me-2"></i>
                Position/Title
              </label>
              <input
                type="text"
                class="form-control"
                name="position"
                id="position"
                placeholder="e.g., Manager, Supervisor, Employee"
              />
            </div>
          </div>
        </div>

        <!-- User Account Section -->
        <div class="form-section">
          <h5 class="section-title">
            <i class="bi bi-key me-2"></i>
            User Account Settings
          </h5>

          <div class="form-check mb-3">
            <input
              class="form-check-input"
              type="checkbox"
              name="create_account"
              id="create_account"
              checked
              onchange="toggleAccountSettings()"
            />
            <label class="form-check-label" for="create_account">
              Create user account for this employee
            </label>
          </div>

          <div id="account_settings">
            <div class="form-group">
              <label class="form-label">
                <i class="bi bi-shield-check me-2"></i>
                Assign Roles
              </label>
              <div class="roles-container">
                <?php foreach ($available_roles as $role): ?>
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      name="roles[]"
                      value="<?= $role['id'] ?>"
                      id="role_<?= $role['id'] ?>"
                      <?= $role['name'] === 'employee' ? 'checked' : '' ?>
                    />
                    <label class="form-check-label" for="role_<?= $role['id'] ?>">
                      <strong><?= htmlspecialchars($role['display_name']) ?></strong>
                      <?php if ($role['description']): ?>
                        <br><small class="text-muted"><?= htmlspecialchars($role['description']) ?></small>
                      <?php endif; ?>
                    </label>
                  </div>
                <?php endforeach; ?>
              </div>
            </div>

            <div class="alert alert-info">
              <i class="bi bi-info-circle me-2"></i>
              <strong>Note:</strong> A secure password will be automatically generated.
              The username and password will be displayed after successful creation.
            </div>
          </div>
        </div>

        <button type="submit" name="add_employee" class="btn btn-primary">
          <i class="bi bi-plus-circle me-2"></i>
          Add Employee
        </button>
      </form>
    </div>
    <?php endif; ?>

    <!-- Existing Employees -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-people me-2"></i>
        Existing Employees
        <span class="badge bg-primary ms-2"><?= count($employees) ?></span>
      </h2>

      <?php if (count($employees) === 0): ?>
        <div class="text-center py-5">
          <i class="bi bi-people" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No employees found. Add your first employee above.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-person me-2"></i>
                  Employee Details
                </th>
                <th>
                  <i class="bi bi-building me-2"></i>
                  Department & Position
                </th>
                <th>
                  <i class="bi bi-key me-2"></i>
                  Account Status
                </th>
                <th width="150">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($employees as $emp): ?>
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar me-3">
                        <?= strtoupper(substr($emp['name'], 0, 1)) ?>
                      </div>
                      <div>
                        <strong><?= htmlspecialchars($emp['name']) ?></strong>
                        <br>
                        <?php if ($emp['email']): ?>
                          <small class="text-muted">
                            <i class="bi bi-envelope me-1"></i>
                            <?= htmlspecialchars($emp['email']) ?>
                          </small>
                          <br>
                        <?php endif; ?>
                        <?php if ($emp['phone']): ?>
                          <small class="text-muted">
                            <i class="bi bi-telephone me-1"></i>
                            <?= htmlspecialchars($emp['phone']) ?>
                          </small>
                          <br>
                        <?php endif; ?>
                        <small class="text-muted">ID: <?= $emp['id'] ?></small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <?php if ($emp['department'] || $emp['position']): ?>
                      <?php if ($emp['department']): ?>
                        <span class="badge bg-secondary mb-1"><?= htmlspecialchars($emp['department']) ?></span>
                        <br>
                      <?php endif; ?>
                      <?php if ($emp['position']): ?>
                        <small class="text-muted"><?= htmlspecialchars($emp['position']) ?></small>
                        <br>
                      <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($emp['hire_date']): ?>
                      <small class="text-muted">
                        <i class="bi bi-calendar me-1"></i>
                        Hired: <?= date('M j, Y', strtotime($emp['hire_date'])) ?>
                      </small>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if ($emp['user_id']): ?>
                      <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>
                        Active Account
                      </span>
                      <br>
                      <small class="text-muted">
                        Username: <strong><?= htmlspecialchars($emp['username']) ?></strong>
                      </small>
                      <br>
                      <?php
                      $user_roles = getUserRoles($mysqli, $emp['user_id']);
                      if (!empty($user_roles)): ?>
                        <small class="text-muted">
                          Roles:
                          <?php foreach ($user_roles as $i => $role): ?>
                            <?= htmlspecialchars($role['display_name']) ?><?= $i < count($user_roles) - 1 ? ', ' : '' ?>
                          <?php endforeach; ?>
                        </small>
                      <?php endif; ?>
                    <?php else: ?>
                      <span class="badge bg-warning">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        No Account
                      </span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="btn-group-vertical" role="group">
                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.edit')): ?>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="editEmployee(<?= $emp['id'] ?>)">
                          <i class="bi bi-pencil me-1"></i>
                          Edit
                        </button>
                      <?php endif; ?>

                      <?php if (!$emp['user_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'users.create')): ?>
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="createAccount(<?= $emp['id'] ?>)">
                          <i class="bi bi-key me-1"></i>
                          Create Account
                        </button>
                      <?php endif; ?>

                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.delete')): ?>
                        <form
                          method="POST"
                          onsubmit="return confirm('Are you sure you want to delete <?= htmlspecialchars($emp['name']) ?>? All related data will be permanently removed.');"
                          style="display:inline;"
                        >
                          <input type="hidden" name="employee_id" value="<?= $emp['id'] ?>" />
                          <button type="submit" name="delete_employee" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash me-1"></i>
                            Delete
                          </button>
                        </form>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    .form-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1.5rem;
      margin: 1.5rem 0;
      border: 1px solid #e9ecef;
    }

    .section-title {
      color: #495057;
      font-size: 1.1rem;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #dee2e6;
    }

    .roles-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 1rem;
      background: white;
    }

    .roles-container .form-check {
      margin-bottom: 0.75rem;
      padding: 0.5rem;
      border-radius: 4px;
      transition: background-color 0.2s;
    }

    .roles-container .form-check:hover {
      background-color: #f8f9fa;
    }

    .btn-group-vertical .btn {
      margin-bottom: 0.25rem;
    }

    .table-container {
      overflow-x: auto;
    }

    @media (max-width: 768px) {
      .btn-group-vertical {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .table th, .table td {
        padding: 0.5rem;
        font-size: 0.9rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function toggleAccountSettings() {
      const checkbox = document.getElementById('create_account');
      const settings = document.getElementById('account_settings');

      if (checkbox.checked) {
        settings.style.display = 'block';
      } else {
        settings.style.display = 'none';
      }
    }

    function editEmployee(employeeId) {
      // TODO: Implement edit functionality
      alert('Edit functionality will be implemented in the next update.');
    }

    function createAccount(employeeId) {
      // TODO: Implement create account functionality
      alert('Create account functionality will be implemented in the next update.');
    }

    // Initialize form state
    document.addEventListener('DOMContentLoaded', function() {
      toggleAccountSettings();
    });
  </script>
</body>
</html>
