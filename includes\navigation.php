<?php
// Get current page for active navigation highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$user_name = $_SESSION['username'] ?? 'User';
$user_permissions = $_SESSION['permissions'] ?? [];
$user_roles = $_SESSION['roles'] ?? [];

// Helper function to check if user has permission (using session data for performance)
function hasSessionPermission($permission) {
    return in_array($permission, $_SESSION['permissions'] ?? []);
}

// Helper function to check if user has any of the permissions
function hasAnySessionPermission($permissions) {
    $user_permissions = $_SESSION['permissions'] ?? [];
    foreach ($permissions as $permission) {
        if (in_array($permission, $user_permissions)) {
            return true;
        }
    }
    return false;
}

// Backward compatibility check for old role system
$is_admin = in_array('super_admin', $user_roles) || in_array('admin', $user_roles) || ($_SESSION['role'] ?? '') === 'admin';
?>

<!-- Global Navigation -->
<nav class="global-nav">
  <div class="nav-container">
    <!-- Brand -->
    <a href="dashboard.php" class="nav-brand">
      <div class="nav-brand-icon">
        <i class="bi bi-clock-history"></i>
      </div>
      <span>AttendanceApp</span>
    </a>

    <!-- Navigation Menu -->
    <ul class="nav-menu" id="navMenu">
      <?php if (hasSessionPermission('dashboard.view')): ?>
      <li class="nav-item">
        <a href="dashboard.php" class="nav-link <?= $current_page === 'dashboard.php' ? 'active' : '' ?>">
          <i class="bi bi-speedometer2"></i>
          <span>Dashboard</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['attendance.clock_in', 'attendance.clock_out'])): ?>
      <li class="nav-item">
        <a href="index.php" class="nav-link <?= $current_page === 'index.php' ? 'active' : '' ?>">
          <i class="bi bi-clock"></i>
          <span>Clock In/Out</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['reports.view', 'reports.view_all'])): ?>
      <li class="nav-item">
        <a href="reports.php" class="nav-link <?= $current_page === 'reports.php' ? 'active' : '' ?>">
          <i class="bi bi-file-text"></i>
          <span>Reports</span>
        </a>
      </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['employees.view', 'employees.create', 'employees.edit'])): ?>
        <li class="nav-item">
          <a href="manage_employees.php" class="nav-link <?= $current_page === 'manage_employees.php' ? 'active' : '' ?>">
            <i class="bi bi-people"></i>
            <span>Employees</span>
          </a>
        </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['shifts.view', 'shifts.create', 'shifts.edit'])): ?>
        <li class="nav-item">
          <a href="manage_shifts.php" class="nav-link <?= $current_page === 'manage_shifts.php' ? 'active' : '' ?>">
            <i class="bi bi-calendar-week"></i>
            <span>Shifts</span>
          </a>
        </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['breaks.view', 'breaks.create', 'breaks.edit'])): ?>
        <li class="nav-item">
          <a href="manage_breaks.php" class="nav-link <?= $current_page === 'manage_breaks.php' ? 'active' : '' ?>">
            <i class="bi bi-pause-circle"></i>
            <span>Breaks</span>
          </a>
        </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['roles.view', 'roles.create', 'roles.edit'])): ?>
        <li class="nav-item">
          <a href="manage_roles.php" class="nav-link <?= $current_page === 'manage_roles.php' ? 'active' : '' ?>">
            <i class="bi bi-shield-check"></i>
            <span>Roles & Permissions</span>
          </a>
        </li>
      <?php endif; ?>

      <?php if (hasAnySessionPermission(['users.view', 'users.create', 'users.edit'])): ?>
        <li class="nav-item">
          <a href="manage_users.php" class="nav-link <?= $current_page === 'manage_users.php' ? 'active' : '' ?>">
            <i class="bi bi-person-gear"></i>
            <span>User Accounts</span>
          </a>
        </li>
      <?php endif; ?>
    </ul>

    <!-- Right Side Menu -->
    <div class="nav-menu">
      <!-- Theme Toggle -->
      <button class="theme-toggle" id="themeToggle" title="Toggle Dark Mode">
        <i class="bi bi-moon" id="themeIcon"></i>
      </button>

      <!-- User Menu -->
      <div class="nav-item">
        <span class="nav-link">
          <i class="bi bi-person-circle"></i>
          <span><?= htmlspecialchars($user_name) ?></span>
        </span>
      </div>

      <!-- Logout -->
      <div class="nav-item">
        <a href="logout.php" class="nav-link" onclick="return confirm('Are you sure you want to logout?')">
          <i class="bi bi-box-arrow-right"></i>
          <span>Logout</span>
        </a>
      </div>

      <!-- Mobile Menu Toggle -->
      <button class="nav-toggle" id="navToggle">
        <i class="bi bi-list"></i>
      </button>
    </div>
  </div>
</nav>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
  <div class="loading-spinner"></div>
</div>

<!-- Notification Container -->
<div id="notificationContainer"></div>

<script>
// Global Navigation JavaScript
document.addEventListener('DOMContentLoaded', function() {
  // Theme Toggle
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = document.getElementById('themeIcon');
  const body = document.body;

  // Load saved theme
  const savedTheme = localStorage.getItem('theme') || 'light';
  body.setAttribute('data-theme', savedTheme);
  updateThemeIcon(savedTheme);

  themeToggle.addEventListener('click', function() {
    const currentTheme = body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);

    // Add bounce animation
    themeToggle.classList.add('animate-bounce');
    setTimeout(() => themeToggle.classList.remove('animate-bounce'), 1000);
  });

  function updateThemeIcon(theme) {
    themeIcon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
  }

  // Mobile Menu Toggle
  const navToggle = document.getElementById('navToggle');
  const navMenu = document.getElementById('navMenu');

  navToggle.addEventListener('click', function() {
    navMenu.classList.toggle('active');
    const icon = navToggle.querySelector('i');
    icon.className = navMenu.classList.contains('active') ? 'bi bi-x' : 'bi bi-list';
  });

  // Close mobile menu when clicking outside
  document.addEventListener('click', function(e) {
    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
      navMenu.classList.remove('active');
      navToggle.querySelector('i').className = 'bi bi-list';
    }
  });

  // Page Transition Animation
  body.classList.add('page-transition');
  setTimeout(() => {
    body.classList.add('loaded');
  }, 100);

  // Add loading state to forms
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function() {
      const submitBtn = form.querySelector('button[type="submit"]');
      if (submitBtn) {
        addLoadingState(submitBtn);
      }
    });
  });
});

// Global Functions
function showLoading() {
  document.getElementById('loadingOverlay').classList.add('active');
}

function hideLoading() {
  document.getElementById('loadingOverlay').classList.remove('active');
}

function addLoadingState(button) {
  button.classList.add('btn-loading');
  const text = button.innerHTML;
  button.setAttribute('data-original-text', text);
  button.innerHTML = '<span class="btn-text">' + text + '</span>';
}

function removeLoadingState(button) {
  button.classList.remove('btn-loading');
  const originalText = button.getAttribute('data-original-text');
  if (originalText) {
    button.innerHTML = originalText;
  }
}

function showNotification(message, type = 'info', duration = 5000) {
  const container = document.getElementById('notificationContainer');
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.innerHTML = `
    <div style="display: flex; align-items: center; gap: 0.5rem;">
      <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
      <span>${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; margin-left: auto; cursor: pointer;">
        <i class="bi bi-x"></i>
      </button>
    </div>
  `;

  container.appendChild(notification);

  // Show notification
  setTimeout(() => notification.classList.add('show'), 100);

  // Auto remove
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => notification.remove(), 300);
  }, duration);
}

// Enhanced form handling with loading states
function handleFormSubmit(form, callback) {
  const submitBtn = form.querySelector('button[type="submit"]');

  form.addEventListener('submit', function(e) {
    e.preventDefault();

    if (submitBtn) addLoadingState(submitBtn);
    showLoading();

    // Simulate processing time or call actual callback
    setTimeout(() => {
      hideLoading();
      if (submitBtn) removeLoadingState(submitBtn);

      if (callback) callback();
    }, 1000);
  });
}
</script>
